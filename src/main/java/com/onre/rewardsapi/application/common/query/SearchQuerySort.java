package com.onre.rewardsapi.application.common.query;

public class SearchQuerySort<T extends Enum<T>> {
    private final T field;
    private final SearchSortDirection direction;
    
    public SearchQuerySort(T field, SearchSortDirection direction) {
        this.field = field;
        this.direction = direction;
    }
    
    public T getField() {
        return field;
    }
    
    public SearchSortDirection getDirection() {
        return direction;
    }
    
    public static <T extends Enum<T>> java.util.List<SearchQuerySort<T>> defaultSort(
            T field, 
            SearchSortDirection direction) {
        return java.util.List.of(new SearchQuerySort<>(field, direction));
    }
}
