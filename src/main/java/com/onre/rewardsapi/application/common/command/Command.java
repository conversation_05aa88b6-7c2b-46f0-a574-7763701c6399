package com.onre.rewardsapi.application.common.command;

import jakarta.validation.Valid;
import java.util.UUID;

public interface Command<R> {
}

public interface CommandHandler<R, C extends Command<R>> {
    Class<C> getCommandType();
    
    R handle(C command);
}

public interface CommandBus {
    <R> R handle(@Valid Command<R> command);
}

public class IdResult {
    private final UUID id;
    
    public IdResult(UUID id) {
        this.id = id;
    }
    
    public UUID getId() {
        return id;
    }
}