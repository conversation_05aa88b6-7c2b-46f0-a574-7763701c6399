package com.onre.rewardsapi.application.common.query;

import jakarta.validation.Valid;

public interface Query<R> {
}

public interface QueryHandler<R, Q extends Query<R>> {
    Class<Q> getQueryType();
    
    R handle(Q query);
}

public interface QueryBus {
    <R> R handle(@Valid Query<R> query);
}

public enum SearchSortDirection {
    ASC, DESC
}

public class SearchQuerySort<T extends Enum<T>> {
    private final T field;
    private final SearchSortDirection direction;
    
    public SearchQuerySort(T field, SearchSortDirection direction) {
        this.field = field;
        this.direction = direction;
    }
    
    public T getField() {
        return field;
    }
    
    public SearchSortDirection getDirection() {
        return direction;
    }
    
    public static <T extends Enum<T>> java.util.List<SearchQuerySort<T>> defaultSort(
            T field, 
            SearchSortDirection direction) {
        return java.util.List.of(new SearchQuerySort<>(field, direction));
    }
}