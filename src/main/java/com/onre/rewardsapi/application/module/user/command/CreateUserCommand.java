package com.onre.rewardsapi.application.module.user.command;

import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.common.command.IdResult;
import jakarta.validation.constraints.NotBlank;

public class CreateUserCommand implements Command<IdResult> {
    @NotBlank
    private final String email;
    
    public CreateUserCommand(String email) {
        this.email = email;
    }
    
    public String getEmail() {
        return email;
    }
}