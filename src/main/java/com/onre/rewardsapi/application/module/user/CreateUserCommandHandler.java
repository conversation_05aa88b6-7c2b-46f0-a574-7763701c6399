package com.onre.rewardsapi.application.module.user;

import com.onre.rewardsapi.application.common.command.CommandHandler;
import com.onre.rewardsapi.application.common.command.IdResult;
import com.onre.rewardsapi.application.module.user.command.CreateUserCommand;
import com.onre.rewardsapi.application.module.user.service.CreateUserService;
import com.onre.rewardsapi.domain.user.User;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class CreateUserCommandHandler implements CommandHandler<IdResult, CreateUserCommand> {
    private final CreateUserService createUserService;
    private final ApplicationEventPublisher applicationEventPublisher;

    public CreateUserCommandHandler(
            CreateUserService createUserService,
            ApplicationEventPublisher applicationEventPublisher) {
        this.createUserService = createUserService;
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public Class<CreateUserCommand> getCommandType() {
        return CreateUserCommand.class;
    }

    @Transactional
    @Override
    public IdResult handle(CreateUserCommand command) {
        User createdUser = createUserService.create(command.getEmail());
        
        // Publish event if needed
        // applicationEventPublisher.publishEvent(new UserCreatedEvent(createdUser.getId()));
        
        return new IdResult(createdUser.getId());
    }
}