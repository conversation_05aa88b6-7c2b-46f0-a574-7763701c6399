package com.onre.rewardsapi.application.module.user;

import com.onre.rewardsapi.application.common.query.QueryHandler;
import com.onre.rewardsapi.application.module.user.finder.UserFinderService;
import com.onre.rewardsapi.application.module.user.query.GetUserByIdQuery;
import com.onre.rewardsapi.domain.user.User;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class GetUserByIdQueryHandler implements QueryHandler<GetUserByIdQuery.Result, GetUserByIdQuery> {
    private final UserFinderService userFinderService;

    public GetUserByIdQueryHandler(UserFinderService userFinderService) {
        this.userFinderService = userFinderService;
    }

    @Override
    public Class<GetUserByIdQuery> getQueryType() {
        return GetUserByIdQuery.class;
    }

    @Transactional(readOnly = true)
    @Override
    public GetUserByIdQuery.Result handle(GetUserByIdQuery query) {
        User user = userFinderService.getById(query.getUserId());
        
        return new GetUserByIdQuery.Result(
            user.getId(),
            user.getEmail()
        );
    }
}