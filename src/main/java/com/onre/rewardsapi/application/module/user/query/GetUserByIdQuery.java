package com.onre.rewardsapi.application.module.user.query;

import com.onre.rewardsapi.application.common.query.Query;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.UUID;

public class GetUserByIdQuery implements Query<GetUserByIdQuery.Result> {
    private final UUID userId;
    
    public GetUserByIdQuery(UUID userId) {
        this.userId = userId;
    }
    
    public UUID getUserId() {
        return userId;
    }
    
    @Schema(name = "GetUserByIdResult")
    public static class Result {
        private final UUID userId;
        private final String email;
        
        public Result(UUID userId, String email) {
            this.userId = userId;
            this.email = email;
        }
        
        public UUID getUserId() {
            return userId;
        }
        
        public String getEmail() {
            return email;
        }
    }
}