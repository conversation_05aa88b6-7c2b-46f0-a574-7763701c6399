package com.onre.rewardsapi.infrastructure.middleware;

import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.common.command.CommandBus;
import com.onre.rewardsapi.application.common.command.CommandHandler;
import jakarta.validation.Valid;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Validated
public class CommandBusMiddleware implements CommandBus {
    private final Map<String, CommandHandler<?, Command<?>>> handlers = new HashMap<>();

    public CommandBusMiddleware(List<CommandHandler<?, ?>> commandHandlers) {
        for (CommandHandler<?, ?> handler : commandHandlers) {
            String commandName = getCommandName(handler.getCommandType());
            if (handlers.containsKey(commandName)) {
                throw new IllegalStateException("Multiple handlers for single command " + commandName);
            }
            
            @SuppressWarnings("unchecked")
            CommandHandler<?, Command<?>> castedHandler = (CommandHandler<?, Command<?>>) handler;
            handlers.put(commandName, castedHandler);
        }
    }

    @Override
    public <R> R handle(@Valid Command<R> command) {
        String commandName = getCommandName(command.getClass());
        
        if (!handlers.containsKey(commandName)) {
            throw new IllegalStateException("No handler for command " + commandName);
        }
        
        CommandHandler<?, Command<?>> handler = handlers.get(commandName);
        
        @SuppressWarnings("unchecked")
        R result = (R) handler.handle(command);
        
        return result;
    }
    
    private String getCommandName(Class<?> commandClass) {
        return commandClass.toString();
    }
}