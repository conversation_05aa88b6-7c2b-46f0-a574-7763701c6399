package com.onre.rewardsapi.infrastructure.middleware;

import com.onre.rewardsapi.application.common.query.Query;
import com.onre.rewardsapi.application.common.query.QueryBus;
import com.onre.rewardsapi.application.common.query.QueryHandler;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Validated
public class QueryBusMiddleware implements QueryBus {
    private final Map<String, QueryHandler<?, Query<?>>> handlers = new HashMap<>();

    public QueryBusMiddleware(List<QueryHandler<?, ?>> queryHandlers) {
        for (QueryHandler<?, ?> handler : queryHandlers) {
            String queryName = getQueryName(handler.getQueryType());
            if (handlers.containsKey(queryName)) {
                throw new IllegalStateException("Multiple handlers for single query " + queryName);
            }
            
            @SuppressWarnings("unchecked")
            QueryHandler<?, Query<?>> castedHandler = (QueryHandler<?, Query<?>>) handler;
            handlers.put(queryName, castedHandler);
        }
    }

    @Override
    public <R> R handle(Query<R> query) {
        String queryName = getQueryName(query.getClass());
        
        QueryHandler<?, Query<?>> handler = handlers.get(queryName);
        if (handler == null) {
            throw new IllegalStateException("No handler for query " + queryName);
        }
        
        @SuppressWarnings("unchecked")
        R result = (R) handler.handle(query);
        
        return result;
    }
    
    private String getQueryName(Class<?> queryClass) {
        return queryClass.toString();
    }
}