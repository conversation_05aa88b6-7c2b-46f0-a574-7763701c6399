package com.cleevio.runwagoapi.common.service;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class AdvisoryLockRepository {

    private final static String LOCK_EXCLUSIVELY_QUERY = "SELECT 1 FROM pg_advisory_xact_lock(hashtext(:lockName))";
    private final static String TRY_LOCK = "SELECT pg_try_advisory_xact_lock(hashtext(:lockName))";

    private final EntityManager entityManager;

    public void lockExclusively(String lockName) {
        entityManager.createNativeQuery(LOCK_EXCLUSIVELY_QUERY)
                .setParameter("lockName", lockName)
                .getSingleResult();
    }

    boolean tryLockExclusively(String lockName) {
        final Query query = entityManager.createNativeQuery(TRY_LOCK)
                .setParameter("lockName", lockName);
        return (boolean) query.getSingleResult();
    }
}
