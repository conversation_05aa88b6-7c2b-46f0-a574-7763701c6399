package com.onre.rewardsapi.adapter.in.client;

import com.onre.rewardsapi.adapter.in.client.request.CreateUserRequest;
import com.onre.rewardsapi.application.common.command.CommandBus;
import com.onre.rewardsapi.application.common.command.IdResult;
import com.onre.rewardsapi.application.common.query.QueryBus;
import com.onre.rewardsapi.application.module.user.command.CreateUserCommand;
import com.onre.rewardsapi.application.module.user.query.GetUserByIdQuery;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/users")
public class UserController {
    private final CommandBus commandBus;
    private final QueryBus queryBus;

    public UserController(CommandBus commandBus, QueryBus queryBus) {
        this.commandBus = commandBus;
        this.queryBus = queryBus;
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public IdResult createUser(@Valid @RequestBody CreateUserRequest request) {
        return commandBus.handle(new CreateUserCommand(request.getEmail()));
    }

    @GetMapping("/{userId}")
    public GetUserByIdQuery.Result getUserById(@PathVariable UUID userId) {
        return queryBus.handle(new GetUserByIdQuery(userId));
    }
}