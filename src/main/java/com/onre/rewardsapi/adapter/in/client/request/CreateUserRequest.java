package com.onre.rewardsapi.adapter.in.client.request;

import jakarta.validation.constraints.NotBlank;

public class CreateUserRequest {
    @NotBlank
    private String email;
    
    // Default constructor for Jackson
    public CreateUserRequest() {
    }
    
    public CreateUserRequest(String email) {
        this.email = email;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
}