# Architecture Rules

## Overview

This document outlines the architectural rules and patterns used in our project. It serves as a guide for developers to understand the structure and design principles of the codebase.

## Architecture Pattern

The project follows a **Hexagonal Architecture** (also known as Ports and Adapters) combined with **Domain-Driven Design (DDD)** and **Command Query Responsibility Segregation (CQRS)** patterns.

### Hexagonal Architecture

The application is structured into several layers:

1. **Domain Layer**: Contains the core business logic, entities, and business rules.
2. **Application Layer**: Contains application services, use cases, and orchestration logic.
3. **Adapter Layer**: Contains adapters for external systems, divided into:
   - **In Adapters**: Handle incoming requests (client's controllers, webhooks)
   - **Out Adapters**: Handle outgoing requests (external service clients, libraries)
4. **Infrastructure Layer**: Contains cross-cutting concerns like configuration, security, and exception handling.

This structure ensures that the core business logic (domain and application layers) remains independent of external concerns like HTTP, databases, or third-party services.

### Newline Rule
Each file should end with a newline!

## Package Structure

```
com.cleevio.cricketclickerapi
├── adapter
│   ├── in
│   │   ├── client
│   │   ├── calendly
│   │   ├── hubspot
│   │   └── stripe
│   └── out
│       ├── jooq
│       ├── file
│       ├── firebase
│       ├── mail
│       └── stripe
├── application
│   ├── common
│   │   ├── constants
│   │   ├── converter
│   │   ├── type
│   │   ├── util
│   │   └── validation
│   └── module
│       ├── profiles
│       ├── connections
│       ├── scheduling
│       ├── booking
│       ├── payment
│       ├── communication
│       ├── notification
│       └── analytics
├── domain
│   ├── common
│   ├── profiles
│   ├── connections
│   ├── scheduling
│   ├── booking
│   ├── payment
│   ├── communication
│   ├── notification
│   ├── analytics
└── infrastructure
    ├── config
    ├── exception
    ├── middleware
    ├── properties
    └── security
```

### Naming Conventions

- **Domain Entities**: Named after business concepts (e.g., `Course`, `Lesson`, `User`)
- **Commands**: Named with verbs in imperative form (e.g., `CreateNewCourseCommand`, `UpdateUserProfileCommand`)
- **Command Handlers**: Named after the command they handle with "Handler" suffix (e.g., `CreateNewCourseCommandHandler`)
- **Queries**: Named with nouns or verbs in interrogative form (e.g., `GetCourseByIdQuery`, `ListActiveUsersQuery`)
- **Query Handlers**: Named after the query they handle with "Handler" suffix (e.g., `GetCourseByIdQueryHandler`)
- **Controllers**: Named after the resource they handle with "Controller" suffix (e.g., `CourseController`, `UserController`)
- **Repositories**: Named after the entity they handle with "Repository" suffix (e.g., `CourseRepository`, `UserRepository`)
- **Exceptions**: Named with descriptive names and "Exception" suffix (e.g., `EntityNotFoundException`, `ValidationException`)

## Component Responsibilities

### Domain Layer

- **Entities**: Represent business objects with identity and lifecycle
- **Value Objects**: Represent immutable concepts without identity
- **Aggregates**: Clusters of entities and value objects treated as a single unit
- **Domain Services**: Contain domain logic that doesn't naturally fit into entities or value objects
- **Domain Events**: Represent something that happened in the domain
- **Repositories (interfaces)**: Define contracts for data access

### Application Layer

- **Commands**: Represent requests to change the state of the system
- **Command Handlers**: Process commands and orchestrate domain logic
- **Queries**: Represent requests to retrieve data from the system
- **Query Handlers**: Process queries and return data
- **Application Services**: Orchestrate domain logic for specific use cases
- **Ports (interfaces)**: Define contracts for external services

### Adapter Layer

- **In Adapters**:
  - **REST Controllers**: Handle HTTP requests and translate them into commands or queries
  - **Webhook Handlers**: Handle incoming webhooks from external services
- **Out Adapters**:
  - **Repositories (implementations)**: Implement data access contracts
  - **External Service Clients**: Communicate with external services

### Infrastructure Layer

- **Configuration**: Configure the application and its components
- **Security**: Handle authentication, authorization, and other security concerns
- **Exception Handling**: Handle and translate exceptions
- **Middleware**: Implement cross-cutting concerns like logging, tracing, and monitoring
- **Properties**: Define configuration properties

## Dependency Rules

1. **Inward Dependency Rule**: Dependencies should only point inward, from outer layers to inner layers:
   - Infrastructure → Adapter → Application → Domain
   - Domain layer should have no dependencies on other layers
   - Application layer can depend on Domain layer
   - Adapter layer can depend on Application and Domain layers
   - Infrastructure layer can depend on all other layers

2. **Package Dependencies**:
   - A module should not depend on another module at the same level
   - Dependencies between modules should be explicit and minimized
   - Common code should be extracted to shared packages

## Exception Handling

1. **Domain Exceptions**: Defined in the domain layer and represent business rule violations
2. **Application Exceptions**: Defined in the application layer and represent application-level errors
3. **Infrastructure Exceptions**: Defined in the infrastructure layer and represent technical errors
4. **Global Exception Handling**: Implemented in the infrastructure layer to translate exceptions into appropriate HTTP responses

## Testing Approach

1. **Unit Tests**: Test individual components in isolation
   - Domain entities and services
   - Command and query handlers
   - Controllers and adapters

2. **Integration Tests**: Test interactions between components
   - Repository implementations
   - External service clients
   - Command and query bus

3. **End-to-End Tests**: Test the entire application flow
   - API endpoints
   - Use cases

## CQRS Pattern

The application follows the Command Query Responsibility Segregation (CQRS) pattern:

1. **Commands**: Represent intentions to change the state of the system
   - Implemented as immutable data classes
   - Processed by command handlers
   - Return minimal results (e.g., IDs, success/failure)

2. **Queries**: Represent requests to retrieve data from the system
   - Implemented as immutable data classes
   - Processed by query handlers
   - Return data transfer objects (DTOs)

3. **Command Bus**: Routes commands to their handlers
   - Validates commands
   - Provides transaction management
   - Handles exceptions

4. **Query Bus**: Routes queries to their handlers
   - Provides caching (if needed)
   - Handles exceptions

## Entity Structure

Domain entities follow a consistent structure:

1. **Base Entity**: `DomainEntity`
   - Has an ID (UUID)
   - Implements equals and hashCode based on ID

2. **Creatable Entity**: `CreatableEntity`
   - Extends `DomainEntity`
   - Adds creation metadata (createdAt, createdBy)

3. **Updatable Entity**: `UpdatableEntity`
   - Extends `CreatableEntity`
   - Adds update metadata (updatedAt, updatedBy)

4. **Soft Deletable Entity**: `SoftDeletableEntity`
   - Extends `UpdatableEntity`
   - Adds soft deletion functionality (deletedAt, isDeleted)
   - Provides methods for soft deletion and restoration

## Conclusion

Following these architectural rules and patterns ensures that the codebase remains maintainable, testable, and scalable. It also facilitates onboarding of new developers by providing a clear structure and set of guidelines.
