# Query Rules

## Overview

This document outlines the rules and patterns for implementing queries in our projects following the Command Query Responsibility Segregation (CQRS) pattern. It serves as a guide for developers to understand how to work with queries for retrieving data from the system.

## CQRS Architecture

Command Query Responsibility Segregation (CQRS) is a pattern that separates read and write operations into different models:

- **Commands**: Represent intentions to change the state of the system (write operations)
- **Queries**: Represent requests to retrieve data from the system (read operations)

This separation allows for optimizing each model independently and provides a clear distinction between operations that modify state and operations that only read state.

### Key Principles

1. **Separation of Concerns**: Read and write operations are handled by separate components.
2. **Immutability**: Queries and their results are immutable data structures.
3. **Single Responsibility**: Each query and query handler has a single, well-defined responsibility.
4. **Type Safety**: The system leverages the type system to ensure that queries return the expected result types.

## Package Structure

```
com.example.gimee
├── application
│   ├── common
│   │   └── query       # Common query interfaces and utilities
│   └── module
│       ├── example     # Module-specific queries and handlers
│       │   ├── query   # Query definitions
│       │   └── finder  # Finder services used by query handlers
│       └────── GetExampleQueryHandler.kt    # Query handler that process the flow logic
├── adapter
│   ├── in
│   │   └── rest        # REST controllers that use queries
│   └── out
│       └── jooq        # JOOQ implementations for data access
└── infrastructure
    └── middleware      # Query bus implementation
```

## Naming Conventions

- **Queries**: Named with nouns or verbs in interrogative form (e.g., `GetUserByIdQuery`, `ListActiveCoursesQuery`)
- **Query Handlers**: Named after the query they handle with "Handler" suffix (e.g., `GetUserByIdQueryHandler`)
- **Finder Services**: Named with the entity they find and "FinderService" suffix (e.g., `UserFinderService`)
- **Query Results**: Defined as nested classes within the query with the name "Result" (e.g., `GetUserByIdQuery.Result`)

## Implementation Patterns

### Query Definition

Queries are defined as immutable data classes that implement the `Query<Result>` interface:

```kotlin
data class GetUserByIdQuery(
    val userId: UUID,
) : Query<GetUserByIdQuery.Result> {

   @Schema(name = "GetUserByIdResult")
    data class Result(
        val userId: UUID,
        val email: String,
        val role: UserRole,
        // Other user properties
    )
}

```

### Query Handler

Query handlers are implemented in root of certain application module. (e.g. application.module.user.GetUserByIdQueryHandler)
Query handlers implement the `QueryHandler<Result, Query>` interface and are responsible for processing queries:

```kotlin
@Component
class GetUserByIdQueryHandler(
    private val userFinderService: UserFinderService,
) : QueryHandler<GetUserByIdQuery.Result, GetUserByIdQuery> {

    override val query = GetUserByIdQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetUserByIdQuery): GetUserByIdQuery.Result {
        val user = userFinderService.getById(query.userId)

        return GetUserByIdQuery.Result(
            userId = user.id,
            email = user.email,
            role = user.role,
            // Map other properties
        )
    }
}

```

### Finder Service

Finder services are responsible for retrieving data from the database:

```kotlin
@Component
class UserFinderService(
    private val getUserPortOut: GetUserPortOut,
) {
    fun getById(userId: UUID): User =
        getUserPortOut.fetch(userId)
            ?: throw UserNotFoundException("User with ID $userId not found")
}

```

### Controller Usage

Controllers use the `QueryBus` to execute queries:

```kotlin
@RestController
@RequestMapping("/users")
class UserController(
    private val queryBus: QueryBus,
) {

    @GetMapping("/{userId}", produces = [ApiVersion.VERSION_1_JSON])
    fun getUserById(@PathVariable userId: UUID): GetUserByIdQuery.Result =
        queryBus(GetUserByIdQuery(userId = userId))

    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchUser(@RequestBody query: SearchUserQuery.Query) = 
		queryBus.handle(SearchUserQuery(query = query))
}

```

## Common Query Patterns

### Simple Query

A simple query retrieves a single entity by its ID:

```kotlin
data class GetProductDetailQuery(
    val productId: UUID,
) : Query<GetProductDetailQuery.Result> {

   @Schema(name = "GetProductDetailResult")
    data class Result(
        val productId: UUID,
        val name: String,
        val description: String,
        val price: BigDecimal,
        // Other product properties
    )
}

```

### List Query

A list query retrieves a collection of entities:

```kotlin
data class ListActiveCoursesQuery(
    val userId: UUID,
) : Query<ListActiveCoursesQuery.Result> {

   @Schema(name = "ListActiveCoursesResult")
    data class Result(
        val courses: List<CourseItem>,
    )

   @Schema(name = "ListActiveCoursesItem")
    data class CourseItem(
        val courseId: UUID,
        val title: String,
        val description: String,
        // Other course properties
    )
}

```

### Search Query with Filtering and Pagination

A search query with filtering and pagination:

```kotlin
data class SearchUserQuery(
   val query: Query,
) : Query<SimplePage<SearchUserQuery.Result>> {

   @Schema(name = "SearchUserQuery")
   data class Query(
      override val pagination: Pagination?,
      override val filter: Filter,
      override val sort: List<SearchQuerySort<SortField>>?,
   ): SearchQuery<SortField, Filter> {

      override fun paginationOrDefault(): Pagination = pagination ?: Pagination.default()
      override fun sortOrDefault(): List<SearchQuerySort<SortField>> =
         sort ?: SearchQuerySort.default(SortField.CREATED_AT, SearchSortDirection.DESC)
   }

   @Schema(name = "SearchUserSortFields")
   enum class SortField {
      USER_ID,
      EMAIL,
      CREATED_AT,
   }

   @Schema(name = "SearchUserFilter")
   data class Filter(
      val fullTextSearch: String?,
   )

   @Schema(name = "SearchUserResult")
   data class Result(
      val userId: UUID,
      val email: String,
      val createdAt: Instant,
   )
}

```

## Best Practices

1. **Keep Queries Simple**: Queries should be simple data classes with only the necessary parameters.
2. **Use Immutable Data Structures**: Queries and their results should be immutable.
3. **Define Result Classes Inside Queries**: Define result classes as nested classes within the query for better organization.
4. **Use Finder Services for Simple Queries**: Use finder services only for simple queries that don't require complex SQL operations or joining multiple tables.
5. **Use JOOQ for Complex Queries**: Use JOOQ implementations for queries that require SQL operations (joins, subqueries, aggregations) or involve multiple tables.
6. **Use Transactional Annotation**: Mark query handlers with `@Transactional(readOnly = true)` for read-only operations.
7. **Handle Exceptions Properly**: Throw domain-specific exceptions when appropriate.
8. **Validate Input**: Use validation annotations on query parameters to ensure valid input.
9. **Document Queries**: Use Swagger annotations to document query parameters and results.

## Complete Example

Here's a complete example of a query implementation:

### Query Definition

```kotlin
data class ListReferralsQuery(
    val userId: UUID,
) : Query<ListReferralsQuery.Result> {

    @Schema(name = "ListReferralsResult")
    data class Result(
        val data: List<ReferralListing>,
    )

    @Schema(name = "ListReferralReferral")
    data class ReferralListing(
        val referralId: UUID,
        val listingOrder: Int,
        val published: Boolean,
        val blurred: Boolean,
        val imageDesktop: ImageResult?,
        val imageMobile: ImageResult?,
        val title: String?,
        val description: String?,
        val linkUrl: String?,
        val rewardCouponCode: String?,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: Boolean,
    )
}

```

### Query Handler

```kotlin
@Component
class SearchUserQueryHandler(
   private val searchUser: SearchUser,
) : QueryHandler<SimplePage<SearchUserQuery.Result>, SearchUserQuery> {

   override val query = SearchUserQuery::class

   override fun handle(query: SearchUserQuery) = searchUser.fetch(query.query)
}

```

### Controller Usage

```kotlin
@RestController
@RequestMapping("/referrals/me")
class ReferralMeController(
    private val queryBus: QueryBus,
) {

    @PostMapping("/list", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listReferrals(@AuthenticationPrincipal userId: UUID): ListReferralsQuery.Result =
        queryBus(ListReferralsQuery(userId = userId))
}

```

## Finder Services vs JOOQ

When implementing queries, it's important to choose the right tool for the job. This section provides guidance on when to use Finder services versus JOOQ.

### When to Use Finder Services

Finder services should be used only for simple queries that don't require complex SQL operations or joining multiple tables. Examples include:

1. **Retrieving a single entity by ID**:
   ```kotlin
   // In UserFinderService
   fun getById(id: UUID): User = userRepository
       .findByIdOrNull(id)
       ?: throw UserNotFoundException("User with ID $id not found")
   ```

2. **Finding entities by a simple condition**:
   ```kotlin
   // In UserFinderService
   fun findByEmail(email: String): User? = userRepository
       .findByEmail(email)
   ```

3. **Retrieving all entities of a certain type**:
   ```kotlin
   // In UserFinderService
   fun findAll(): List<User> = userRepository
       .findAll()
   ```

4. **Checking if an entity exists**:
   ```kotlin
   // In UserFinderService
   fun existsById(id: UUID): Boolean = userRepository
       .existsById(id)
   ```

5. **Finding entities by a set of IDs**:
   ```kotlin
   // In UserFinderService
   fun findAllByIds(ids: Set<UUID>): List<User> = userRepository
       .findAllById(ids)
   ```

### When to Use JOOQ

JOOQ should be used for more complex queries that require SQL operations or involve multiple tables. Examples include:

1. **Joining multiple tables**:
   ```kotlin
   dslContext
       .select(
           USER.ID,
           USER.EMAIL,
           PROFILE.BIO
       )
       .from(USER)
       .leftJoin(PROFILE).on(USER.ID.eq(PROFILE.USER_ID))
       .where(USER.ID.eq(userId))
       .fetchOne()
   ```

2. **Aggregations and grouping**:
   ```kotlin
   dslContext
       .select(
           COURSE.COURSE_CATEGORY,
           DSL.count().`as`("course_count")
       )
       .from(COURSE)
       .where(COURSE.DELETED_AT.isNull())
       .groupBy(COURSE.COURSE_CATEGORY)
       .fetch()
   ```

3. **Subqueries**:
   ```kotlin
   val modulesCount = field(
       selectCount()
           .from(COURSE_MODULE)
           .where(COURSE_MODULE.COURSE_ID.eq(COURSE.ID))
           .and(COURSE_MODULE.DELETED_AT.isNull()),
   ).`as`("modules_count")
   ```

4. **Complex filtering or search**:
   ```kotlin
   filter.autocompleteCondition(
       COURSE.TITLE,
       COURSE.trader.FIRST_NAME,
       COURSE.trader.LAST_NAME,
   )
   ```

5. **Pagination with complex conditions**:
   ```kotlin
   dslContext
       .select(/* fields */)
       .from(COURSE)
       .whereWithInfiniteScroll(
           conditions = conditions,
           infiniteScroll = infiniteScroll,
           idFieldSelector = COURSE.ID,
       )
       .fetch()
   ```

### Choosing Between Finder Services and JOOQ

When deciding whether to use a Finder service or JOOQ for a particular query, consider the following:

1. **Complexity**: If the query is simple and can be expressed using Spring Data JPA repository methods, use a Finder service. If it requires complex SQL operations, use JOOQ.
2. **Number of Tables**: If the query involves only one table, a Finder service might be sufficient. If it involves multiple tables, JOOQ is usually the better choice.
3. **Performance**: For performance-critical queries that need to be optimized with specific SQL features, JOOQ provides more control.
4. **Readability**: JOOQ queries can be more readable for complex operations because they closely resemble SQL.
5. **Maintainability**: Simple queries are more maintainable with Finder services, while complex queries are more maintainable with JOOQ.

Remember that the goal is to use the right tool for the job. Don't use a Finder service for a complex query just because it's simpler to implement, and don't use JOOQ for a simple query just because it's more powerful.

## Conclusion

Following these query rules and patterns ensures that data retrieval code is maintainable, testable, and consistent across the project. It also facilitates onboarding of new developers by providing clear guidelines for working with queries in a CQRS architecture.
Don't check if JOOQ compiles, contains all imports, or has unresolved references.
