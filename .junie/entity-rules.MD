# Entity Rules

## Overview

This document outlines the rules and patterns for implementing domain entities in our projects following Domain-Driven Design (DDD) principles. It serves as a guide for developers to understand how to design and implement entities that represent the core business objects in the system.

## Domain-Driven Design

Domain-Driven Design (DDD) is an approach to software development that focuses on creating a rich domain model that reflects the business domain. Entities are a key concept in DDD:

- **Entities**: Objects with a distinct identity that runs through time and different states
- **Value Objects**: Immutable objects that describe aspects of the domain with no identity
- **Aggregates**: Clusters of entities and value objects treated as a single unit
- **Repositories**: Provide methods to access and persist aggregates

## Entity Structure

Domain entities follow a consistent structure with a hierarchy of base classes:

### Base Entity Classes

1. **DomainEntity**: The foundation for all entities
   - Has an ID (UUID)
   - Implements equals and hashCode based on ID

2. **CreatableEntity**: Extends DomainEntity
   - Adds creation metadata (createdAt)
   - Automatically tracks creation timestamp and user

3. **UpdatableEntity**: Extends CreatableEntity
   - Adds update metadata (updatedAt)
   - Automatically tracks update timestamp and user

4. **SoftDeletableEntity**: Extends UpdatableEntity
   - Adds soft deletion functionality (deletedAt)
   - Provides methods for soft deletion and restoration

Simple example:
```kotlin
@Table(name = "\"user\"")
@Entity
class User private constructor(
    id: UUID,
    val email: String,
	state: UserState,
) : UpdatableEntity(id) {
	
	var state: UserState = state
	    private set

    companion object {
        fun create(
            id: UUID = UUIDv7.randomUUID(),
            email: String,
        ) = User(
            id = id,
            email = email.lowercase(),
			state = UserState.PENDING,
        )
    }
   
   fun verify() { 
        require(state == UserState.PENDING) { "User is not pending" }
        this.state = UserState.VERIFIED
   }
}

enum class UserState {
        PENDING,
        VERIFIED,
}

@Repository
interface UserRepository : JpaRepository<User, UUID> {
    fun findByEmailIgnoreCase(email: String): User?
    fun existsByEmailIgnoreCase(email: String): Boolean
}

```

## Entity Implementation Patterns

### Factory Methods

Entities should use private constructors and factory methods to create new instances. This ensures that entities are always created in a valid state and encapsulates the creation logic.

```kotlin
class Course private constructor(
    id: UUID,
    listingOrder: Int,
    published: Boolean,
    title: String,
    // Other properties
) : SoftDeletableEntity(id) {

    // Properties with private setters
    var listingOrder: Int = listingOrder
        private set

    var published: Boolean = published
        private set

    var title: String = title
        private set

    // Factory method
    companion object {
        fun newCourse(
            id: UUID = UUIDv7.randomUUID(),
            listingOrder: Int,
            title: String,
            // Other parameters
        ) = Course(
            id = id,
            listingOrder = listingOrder,
            published = false,
            title = title,
            // Other properties
        )
    }
}

```

### Immutability and Encapsulation

Entities should be immutable from the outside, with all properties having private setters. Changes to the entity state should only be allowed through specific methods that encapsulate business rules.

```kotlin
class Course private constructor(
    id: UUID,
    listingOrder: Int,
    published: Boolean,
    title: String,
    // Other properties
) : SoftDeletableEntity(id) {

    // Properties with private setters
    var listingOrder: Int = listingOrder
        private set

    // Methods for changing state
    fun updateListingOrder(newOrder: Int) {
        checkListingOrderIsPositiveOrZero(newOrder)
        this.listingOrder = newOrder
    }

    fun update(
        title: String,
        // Other parameters
    ) {
        this.title = title
        // Update other properties
    }

    fun publish() {
        checkImagesPresent()
        this.published = true
    }

    fun unpublish() {
        this.published = false
    }
}

```

### Domain Validation

Entities should validate their state to ensure they always remain in a valid state. Validation should be performed in constructors, factory methods, and methods that change the entity state.

```kotlin
class Course private constructor(
    id: UUID,
    listingOrder: Int,
    // Other properties
) : SoftDeletableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
    }

    // Validation methods
    private fun checkListingOrderIsPositiveOrZero(listingOrder: Int) {
        if (listingOrder >= 0) return

        throw CourseOrderCannotBeNegativeException("Listing order cannot be negative: '$listingOrder'")
    }

    private fun checkImagesPresent() {
        if (introPictureDesktopFileId == null) {
            throw CourseMissingPictureException("Course: '$id' does not have DESKTOP picture")
        }
        if (introPictureMobileFileId == null) {
            throw CourseMissingPictureException("Course: '$id' does not have MOBILE picture")
        }
    }
}

```

### Domain-Specific Exceptions

Entities should throw domain-specific exceptions when validation fails or business rules are violated. These exceptions should be defined in the domain layer and provide meaningful error messages.

```kotlin
@ResponseStatus(HttpStatus.NOT_FOUND)
class UserNotFoundException(message: String) : ApiException(
   reason = ExtendedErrorReasonType.USER_NOT_FOUND,
   message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class UserAlreadyExistsException(message: String) : ApiException(
   reason = ExtendedErrorReasonType.USER_ALREADY_EXISTS,
   message = message,
)

```

Don't add unused exceptions!

## Entity Relationships

### Foreign Key References

Relationships between entities are typically implemented using foreign key references rather than direct object references. This approach is more compatible with JPA and allows for more flexible querying.

```kotlin
class CourseModule private constructor(
    id: UUID,
    val courseId: UUID,  // Foreign key reference to Course
    // Other properties
) : SoftDeletableEntity(id) {

    // Methods for validating relationships
    fun checkRelatedToCourse(courseId: UUID) {
        if (this.courseId != courseId) {
            throw CourseModuleNotRelatedToCourseException("Course module: '$id' is not related to course: '$courseId'")
        }
    }
}
```

### Embedded Value Objects

Value objects that are part of an entity can be embedded using JPA's @Embedded annotation. This allows for encapsulating related attributes as a single value object.

```kotlin
class CourseModule private constructor(
    id: UUID,
    rewardButton: AppButtonWithLink?,
    // Other properties
) : SoftDeletableEntity(id) {

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "text", column = Column(name = "reward_button_text")),
        AttributeOverride(name = "color", column = Column(name = "reward_button_color")),
        AttributeOverride(name = "linkUrl", column = Column(name = "reward_button_link_url")),
    )
    var rewardButton: AppButtonWithLink? = rewardButton
        private set
}
```

## Domain services
For each new entity create also service that will create this entity.
```kotlin
@Service
class CreateUserService(
    private val userRepository: UserRepository,
) {
    @Transactional
    fun create(email: String): User {
        if (userRepository.existsByEmailIgnoreCase(email = email)) {
            throw UserAlreadyExistsException(message = "User with email $email already exists")
        }

        return userRepository.save(
            User.create(
                email = email,
            ),
        )
    }
}
```

## Repository Pattern

Repositories provide an abstraction layer for accessing and persisting entities. They should be defined as interfaces in the domain layer and implemented in the infrastructure layer.
Don't add unused methods! Add a method to the repository only if it is required.

```kotlin
@Repository
interface CourseRepository : JpaRepository<Course, UUID> {
    @Query(
        """
        SELECT MAX(c.listingOrder) 
        FROM Course c 
        WHERE c.courseCategory = :courseCategory AND c.deletedAt IS NULL
    """,
    )
    fun findMaxListingOrderNonDeleted(courseCategory: CourseCategory): Int?

    fun countByCourseCategoryAndDeletedAtIsNull(courseCategory: CourseCategory): Long

    fun findByIdAndCourseCategoryAndDeletedAtIsNull(
        id: UUID,
        courseCategory: CourseCategory,
    ): Course?

    // Other query methods
}
```

## Best Practices

1. **Use UUIDs for Entity IDs**: UUIDs provide globally unique identifiers that can be generated without database coordination.
2. **Implement Equals and HashCode Based on ID**: Entities should be compared based on their identity (ID) rather than their state.
3. **Use Private Constructors and Factory Methods**: This ensures that entities are always created in a valid state and encapsulates the creation logic.
4. **Make Properties Immutable from Outside**: Use private setters and provide specific methods for changing entity state.
5. **Validate Entity State**: Ensure that entities always remain in a valid state by validating their state in constructors, factory methods, and state-changing methods.
6. **Use Domain-Specific Exceptions**: Throw domain-specific exceptions when validation fails or business rules are violated.
7. **Implement Soft Deletion**: Use soft deletion (marking entities as deleted rather than physically removing them) to maintain data integrity and audit trails.
8. **Keep Business Logic in Entities**: Entities should encapsulate business rules and logic related to their state and behavior.
9. **Use Value Objects for Complex Attributes**: Encapsulate related attributes as value objects to improve code organization and maintainability.
10. **Document Entity Purpose**: Include comments or documentation that explains the business purpose of the entity.

## Complete Example

Here's a complete example of an entity implementation:

```kotlin
@Entity
@DynamicUpdate
class Course private constructor(
    id: UUID,
    listingOrder: Int,
    published: Boolean,
    title: String,
    courseCategory: CourseCategory,
    visibleToTiers: List<StudentTier>,
    visibleToDiscordUsers: Boolean,
    description: String,
    traderId: UUID,
    color: Color,
    thumbnailUrl: String,
    thumbnailAnimationUrl: String,
    trailerUrl: String,
    introPictureDesktopFileId: UUID?,
    introPictureMobileFileId: UUID?,
) : SoftDeletableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
    }

    var listingOrder: Int = listingOrder
        private set

    var published: Boolean = published
        private set

    var title: String = title
        private set

    @Enumerated(EnumType.STRING)
    var courseCategory: CourseCategory = courseCategory
        private set

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "text[]")
    private val _visibleToTiers: MutableList<StudentTier> = visibleToTiers.toMutableList()
    val visibleToTiers: List<StudentTier>
        get() = _visibleToTiers.toList()

    var visibleToDiscordUsers: Boolean = visibleToDiscordUsers
        private set

    var description: String = description
        private set

    var traderId: UUID = traderId
        private set

    @Enumerated(EnumType.STRING)
    var color: Color = color
        private set

    var thumbnailUrl: String = thumbnailUrl
        private set

    var thumbnailAnimationUrl: String = thumbnailAnimationUrl
        private set

    var trailerUrl: String = trailerUrl
        private set

    @File(type = FileType.COURSE_DESKTOP_INTRO_PHOTO)
    var introPictureDesktopFileId: UUID? = introPictureDesktopFileId
        private set

    @File(type = FileType.COURSE_MOBILE_INTRO_PHOTO)
    var introPictureMobileFileId: UUID? = introPictureMobileFileId
        private set

   companion object {
      fun newCourse(
         id: UUID = UUIDv7.randomUUID(),
         listingOrder: Int,
         title: String,
         courseCategory: CourseCategory,
         visibleToTiers: List<StudentTier>,
         visibleToDiscordUsers: Boolean,
         description: String,
         traderId: UUID,
         color: Color,
         thumbnailUrl: String,
         thumbnailAnimationUrl: String,
         trailerUrl: String,
      ) = Course(
         id = id,
         listingOrder = listingOrder,
         published = false,
         title = title,
         courseCategory = courseCategory,
         visibleToTiers = visibleToTiers,
         visibleToDiscordUsers = visibleToDiscordUsers,
         description = description,
         traderId = traderId,
         color = color,
         thumbnailUrl = thumbnailUrl,
         thumbnailAnimationUrl = thumbnailAnimationUrl,
         trailerUrl = trailerUrl,
         introPictureDesktopFileId = null,
         introPictureMobileFileId = null,
      )
   }

   fun update(
      title: String,
      courseCategory: CourseCategory,
      visibleToTiers: List<StudentTier>,
      visibleToDiscordUsers: Boolean,
      description: String,
      traderId: UUID,
      color: Color,
      thumbnailUrl: String,
      thumbnailAnimationUrl: String,
      trailerUrl: String,
   ) {
      this.title = title
      this.courseCategory = courseCategory
      this._visibleToTiers.replaceContents(visibleToTiers)
      this.visibleToDiscordUsers = visibleToDiscordUsers
      this.description = description
      this.traderId = traderId
      this.color = color
      this.thumbnailUrl = thumbnailUrl
      this.thumbnailAnimationUrl = thumbnailAnimationUrl
      this.trailerUrl = trailerUrl
   }
}

```

## Database Migrations

Database migrations are used to evolve the database schema over time in a controlled and versioned manner. The project uses Flyway for database migrations, which automatically applies migrations in order based on their version numbers.

### How Migrations Work

1. **Versioned Migrations**: Each migration file is named with a version number and a description (e.g., `V1__app_user_and_file.sql`, `V28__firebase_identifier_not_null.sql`).
2. **Sequential Execution**: Migrations are executed in order based on their version numbers.
3. **Idempotency**: Each migration is applied only once, and Flyway keeps track of which migrations have been applied in a special table called `flyway_schema_history`.
4. **Transaction Management**: Each migration is executed within a transaction, ensuring that it is either fully applied or not applied at all.

### Naming Conventions for Database Objects

Database objects such as indexes, foreign keys, and unique constraints should follow these naming conventions:

#### Current Convention

- **Indexes**: `"table_name__column_name_idx"` (e.g., `"course__trader_idx"`)
- **Unique Indexes**: `"table_name__column_name_ui"` (e.g., `"app_user__firebase_identifier_ui"`)
- **Foreign Keys**: Descriptive names related to the referenced table or column (e.g., `"trader"`, `"intro_picture_desktop"`)

#### New Convention

For new projects, use the following naming convention:

- **Indexes**: 20 random characters followed by `_ix` (e.g., `"775fdc295ea745bb9477_ix"`)
- **Foreign Keys**: 20 random characters followed by `_fk` (e.g., `"775fdc295ea745bb9477_fk"`)
- **Unique Constraints**: 20 random characters followed by `_ux` (e.g., `"775fdc295ea745bb9477_ux"`)

### Example Migration

Here's an example of a migration file that creates a table and adds indexes and foreign keys:

```sql
CREATE TABLE user_setting
(
    id         UUID PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    version    INTEGER     NOT NULL,
    user_id    UUID        NOT NULL,
    key        TEXT        NOT NULL,
    value      TEXT        NOT NULL,
    types      TEXT[]      NOT NULL,

    CONSTRAINT "ff2376c1f3dc4545b559_fk" FOREIGN KEY (user_id) REFERENCES user (id)
);

CREATE INDEX "775fdc295ea745bb9477_ix" ON user_setting (user_id);
CREATE UNIQUE INDEX "73ac78a5d6574cf5be08_ux" ON user_setting (user_id, key);

```

Make sure the index name is unique. You can use the first 20 characters from a random UUID.
Use `uuidgen` in the terminal to generate a random UUID (example: generated random UUID: `9695881B-18CE-4BE4-AAA8-104521239F23` -> first 20 characters: `9695881B18CE4BE4AAA8`).

### Best Practices for Migrations

1. **Keep Migrations Small**: Each migration should make a small, focused change to the database schema.
2. **Make Migrations Forward-Only**: Avoid writing migrations that undo previous migrations. Instead, write new migrations that make the desired changes.
3. **Test Migrations**: Test migrations on a development database before applying them to production.
4. **Document Migrations**: Include comments in migration files to explain complex changes.
5. **Use Transactions**: Ensure that migrations are executed within transactions to maintain data integrity.
6. **Handle Data Migration**: When changing the structure of existing tables, consider how to migrate existing data.
7. **Use Descriptive Names**: Give migrations descriptive names that indicate what they do.
8. **Follow Naming Conventions**: Use consistent naming conventions for database objects.

## Final Rules
- Don't add unused methods to the repository or unused exceptions! 
