# Command Rules

## Overview

This document outlines the rules and patterns for implementing commands in our projects following the Command Query Responsibility Segregation (CQRS) pattern. It serves as a guide for developers to understand how to work with commands for modifying data in the system.

## CQRS Architecture

Command Query Responsibility Segregation (CQRS) is a pattern that separates read and write operations into different models:

- **Commands**: Represent intentions to change the state of the system (write operations)
- **Queries**: Represent requests to retrieve data from the system (read operations)

This separation allows for optimizing each model independently and provides a clear distinction between operations that modify state and operations that only read state.

### Key Principles

1. **Separation of Concerns**: Read and write operations are handled by separate components.
2. **Immutability**: Commands are immutable data structures.
3. **Single Responsibility**: Each command and command handler has a single, well-defined responsibility.
4. **Type Safety**: The system leverages the type system to ensure that commands return the expected result types.

## Package Structure

```
com.cleevio.cricketclickerapi
├── adapter
│   └── in
│       └── client
│           └── request
├── application
│   ├── common
│   │   └── command     # Common command interfaces and utilities
│   └── module
│       ├── example     # Module-specific commands and handlers
│       │   ├── command # Command definitions
│       │   └── service # Domain services used by command handlers
│       └────── ExampleCreateCommandHandler.kt    # Query handler that process the flow logic
├── adapter
│   ├── in
│   │   └── client      # REST controllers that use commands
└── infrastructure
    └── middleware      # Command bus implementation
```

## Naming Conventions

- **Commands**: Named with verbs in imperative form (e.g., `CreateUserCommand`, `UpdateProfileCommand`)
- **Command Handlers**: Named after the command they handle with "Handler" suffix (e.g., `CreateUserCommandHandler`)
- **Domain Services**: Named with the action they perform and "Service" suffix (e.g., `CreateUserService`)
- **Command Results**: For commands that create resources, use `IdResult` for returning ID of new resource

## Implementation Patterns

### Command Definition

Commands are defined as immutable data classes that implement the `Command<Result>` interface:

```kotlin
data class CreateLessonCommand(
	val courseId: UUID,
	val courseModuleId: UUID,
	@field:NotBlank val title: String,
	@field:NullOrNotBlank val description: String?,
	@field:NotBlank val videoUrl: String,
	@field:PositiveOrZero val durationInSeconds: Int,
	@field:NotBlank val thumbnailUrl: String,
	@field:NotBlank val thumbnailAnimationUrl: String,
	val attachments: List<@Valid LessonAttachmentInput>,
) : Command<IdResult>

```

### Command Handler

Command handlers are implemented in root of certain application module. (e.g. application.module.lesson.CreateLessonCommandHandler)
Command handlers implement the `CommandHandler<Result, Command>` interface and are responsible for processing commands:

```kotlin
@Component
class CreateLessonCommandHandler(
    private val createLessonService: CreateLessonService,
    private val createLessonAttachmentService: CreateLessonAttachmentService,
    private val courseModuleFinderService: CourseModuleFinderService,
    private val lessonFinderService: LessonFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, CreateLessonCommand> {

    override val command = CreateLessonCommand::class

    @Transactional
    @Lock(module = Locks.Lesson.MODULE, lockName = Locks.Lesson.CREATE)
    override fun handle(@LockFieldParameter("courseModuleId") command: CreateLessonCommand): IdResult {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .apply { checkRelatedToCourse(command.courseId) }

        val currentMaxOrder = lessonFinderService.findMaxListingOrderNonDeleted(command.courseModuleId) ?: 0

        val createdLesson = createLessonService.create(
            courseModuleId = command.courseModuleId,
            listingOrder = currentMaxOrder + 1,
            title = command.title,
            videoUrl = command.videoUrl,
            description = command.description,
            durationInSeconds = command.durationInSeconds,
            thumbnailUrl = command.thumbnailUrl,
            thumbnailAnimationUrl = command.thumbnailAnimationUrl,
        ).also { createdLesson: Lesson ->
            createLessonAttachmentService.createAttachments(
                lessonId = createdLesson.id,
                attachments = command.attachments
                    .filter { it: LessonAttachmentInput -> it.forCreate }
                    .mapIndexed { idx: Int, attachmentInput: LessonAttachmentInput -> attachmentInput.toValue(idx + 1) },
            )
        }

        applicationEventPublisher.publishEvent(LessonCreatedEvent(lessonId = createdLesson.id))

        return IdResult(createdLesson.id)
    }
}

```

For updates, do not call the repository. The Spring Boot persistence context will handle it automatically.
```kotlin
@Component
class UpdateCommentCommandHandler(
    private val commentFinderService: CommentFinderService,
) : CommandHandler<Unit, UpdateCommentCommand> {

    override val command = UpdateCommentCommand::class

    @Transactional
    @Lock(module = Locks.Comment.MODULE, lockName = Locks.Comment.UPDATE)
    override fun handle(@LockFieldParameter("commentId") command: UpdateCommentCommand) {
        commentFinderService
            .getById(command.commentId)
            .ownerUpdate(appUserId = command.appUserId, text = command.text)
    }
}

```

### Domain Service

Domain services encapsulate business logic and are used by command handlers:

```kotlin
@Component
class CreateLessonService(
    private val lessonRepository: LessonRepository,
) {
    fun create(
        courseModuleId: UUID,
        listingOrder: Int,
        title: String,
        videoUrl: String,
        description: String?,
        durationInSeconds: Int,
        thumbnailUrl: String,
        thumbnailAnimationUrl: String,
    ) = lessonRepository.save(
            Lesson.create(
                courseModuleId = courseModuleId,
                listingOrder = listingOrder,
                title = title,
                videoUrl = videoUrl,
                description = description,
                durationInSeconds = durationInSeconds,
                thumbnailUrl = thumbnailUrl,
                thumbnailAnimationUrl = thumbnailAnimationUrl,
            )
    )
}

```

They are needed only for creating and deleting entities. 
Only checks that pertain solely to the specific domain can be performed on domain services. 
For example, when creating a user, checking if the email already exists.

### Controller Usage

Controllers use the `CommandBus` to execute commands:

```kotlin
@RestController
@RequestMapping("/lessons")
class LessonController(
    private val commandBus: CommandBus,
) {

    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createLesson(@Valid @RequestBody request: CreateLessonRequest): IdResult =
        commandBus(request.toCommand())
}

```

## Common Command Patterns

### Create Command

A create command creates a new entity and returns its ID:

```kotlin
data class CreateLessonCommand(
    val courseId: UUID,
    val courseModuleId: UUID,
    @field:NotBlank val title: String,
    @field:NullOrNotBlank val description: String?,
    @field:NotBlank val videoUrl: String,
    @field:PositiveOrZero val durationInSeconds: Int,
    @field:NotBlank val thumbnailUrl: String,
    @field:NotBlank val thumbnailAnimationUrl: String,
    val attachments: List<@Valid LessonAttachmentInput>,
) : Command<IdResult>

```

### Update Command

An update command modifies an existing entity and typically returns Unit:

```kotlin
data class UpdateHighlightCommand(
    val highlightId: UUID,
    @field:NullOrNotBlank val title: String?,
    @field:NullOrNotBlank val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NullOrNotBlank val linkUrl: String?,
    @field:Valid val button: AppButtonInput?,
) : Command<Unit>

```

### Delete Command

A delete command removes an entity and typically returns Unit:

```kotlin
data class DeleteHighlightCommand(
    val highlightId: UUID,
) : Command<Unit>

```

### State Change Command

A state change command modifies the state of an entity without changing its core properties:

```kotlin
data class PublishHighlightCommand(
    val highlightId: UUID,
) : Command<Unit>

```

### Reordering Command

A reordering command changes the order of a collection of entities:

```kotlin
data class ReorderHighlightsCommand(
    val highlightOrderings: List<@Valid HighlightOrderingInput>,
) : Command<Unit> {
    data class HighlightOrderingInput(
        val highlightId: UUID,
        @field:PositiveOrZero val newListingOrder: Int,
    )
}

```

## Best Practices

1. **Keep Commands Simple**: Commands should be simple data classes with only the necessary parameters.
2. **Use Immutable Data Structures**: Commands should be immutable.
3. **Define Nested Classes for Complex Inputs**: Define nested classes within the command for complex input structures.
4. **Use Domain Services for Business Logic**: Encapsulate business logic in domain services.
5. **Use Transactional Annotation**: Mark command handlers with `@Transactional` to ensure atomic operations. Do not use `@Transactional` if a third-party service is called.
6. **Handle Concurrency**: Use locking mechanisms to handle concurrent modifications.
7. **Publish Events**: Publish domain events after successful command execution (if it is needed).
8. **Validate Input**: Use validation annotations on command parameters to ensure valid input.
9. **Document Commands**: Use Swagger annotations to document command parameters.
10. **Return Appropriate Results**: Return IdResult for creation operations, Unit for operations that don't need to return data.

## Conclusion

Following these command rules and patterns ensures that data modification code is maintainable, testable, and consistent across the project. It also facilitates onboarding of new developers by providing clear guidelines for working with commands in a CQRS architecture.
