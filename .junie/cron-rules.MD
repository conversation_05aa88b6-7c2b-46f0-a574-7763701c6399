# Cron Jobs (Triggers) Rules

This document outlines the guidelines and best practices for implementing cron jobs (scheduled tasks) in our projects, with a focus on naming conventions and locking mechanisms.

## Naming Conventions

### Class Naming

- Scheduled task classes should be suffixed with `Trigger` (e.g., `CheckPaperTradeToSellTrigger`)
- Place trigger classes in a `scheduled` package within the relevant module

### Method Naming

- Use descriptive method names that clearly indicate the purpose of the scheduled task
- Examples: `checkPaymentEvents()`, `scheduleItemProcessing()`, `botTrigger()`

### Lock Naming

- Lock names should be in UPPERCASE with underscores separating words
- Lock names should be descriptive and reflect the purpose of the task
- Consider defining lock names as constants at the top of the file for reuse
- Examples: `CHECK_PAPER_TRADE_TO_SELL`, `PROCESS_BOT_TRANSACTION_STATUSES`, `CHECK_PAYMENT_EVENTS`

## Locking Mechanisms

We use ShedLock for distributed locking to ensure that scheduled tasks are executed only once at the same time across multiple instances.

### Task-Level Locking

- Always use `@SchedulerLock` annotation on scheduled methods
- Specify a unique lock name
- Set appropriate lock durations:
  - `lockAtLeastFor`: Minimum time the lock should be kept (prevents execution on other nodes if the task completes quickly)
  - `lockAtMostFor`: Maximum time the lock should be kept (safety net in case the node dies)

- Example with hardcoded values:
```kotlin
@SchedulerLock(name = "CHECK_PAPER_TRADE_TO_SELL", lockAtLeastFor = "PT55S")
```

- Example with configurable values:
```kotlin
@SchedulerLock(
    name = "CHECK_PAYMENT_EVENTS",
    lockAtLeastFor = "${gimee.subscription.payment.monitoring.lock-for}",
    lockAtMostFor = "${gimee.subscription.payment.monitoring.lock-for}"
)
```

## Scheduling Options

### Cron Expressions

- Prefer configurable cron expressions over hardcoded ones:
```kotlin
@Scheduled(cron = "${gimee.trading.check-paper-trade.cron}")
```

### Fixed Rate/Delay

- For tasks that need to run at fixed intervals, use `fixedRateString` or `fixedDelayString`:
```kotlin
@Scheduled(fixedRateString = "${gimee.subscription.payment.monitoring.scheduled-fixed-delay}")
```

## Implementation Best Practices

### Logging

- Log the beginning and end of the scheduled task execution:
```kotlin
logger.info("Check PaperTrade sell cron begin, there are ${paperTrades.size} paper trades to check.")
// Task execution
logger.info("Check PaperTrade sell cron finished.")
```

### Error Handling

- Wrap task execution in try-catch blocks to prevent failures from stopping the scheduler:
```kotlin
try {
    checkPaperTradeStateService.check(paperTrade.id)
} catch (e: Exception) {
    logger.error("failed to process PaperTrade with id=${paperTrade.id}", e)
}
```

### Concurrency Control

- For tasks processing multiple items, consider using concurrency control:
```kotlin
private val concurrencyLimiter = Semaphore(permits = 10) // Max 10 parallel chunks

runBlocking {
    paperTrades.chunked(100).forEach { batch ->
        batch.map { paperTrade ->
            scope.async {
                concurrencyLimiter.withPermit {
                    // Process item
                }
            }
        }.awaitAll()
    }
}
```

### Monitoring

- Consider adding monitoring for important scheduled tasks:
```kotlin
@SentryTransaction(operation = "scheduled.process-transaction-statuses")
```

## Examples

### Basic Cron Job with Locking

```kotlin
@Component
class TransactionStatusProcessingTrigger(
    private val botTransactionProcessingService: TransactionStatusProcessingService,
) {

    private val logger = logger()

    @Scheduled(cron = "${gimee.transaction.tx-status-processing.cron}")
    @SchedulerLock(
        name = "PROCESS_BOT_TRANSACTION_STATUSES",
        lockAtLeastFor = "${gimee.transaction.tx-status-processing.lock-for}",
        lockAtMostFor = "${gimee.transaction.tx-status-processing.lock-for}",
    )
    fun botTrigger() {
        logger.debug("PROCESS_BOT_TRANSACTION_STATUSES cron started")
        botTransactionProcessingService.process()
        logger.debug("PROCESS_BOT_TRANSACTION_STATUSES cron ended")
    }
}

```

### Cron Job with Batch Processing and Concurrency Control

```kotlin
@Component
class CheckPaperTradeToSellTrigger(
    private val paperTradeFinderService: PaperTradeFinderService,
    private val checkPaperTradeStateService: CheckPaperTradeStateService,
) {

    private val logger = logger()

    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private val concurrencyLimiter = Semaphore(permits = 10) // Max 10 parallel chunks

    @SchedulerLock(name = "CHECK_PAPER_TRADE_TO_SELL", lockAtLeastFor = "PT55S")
    @Scheduled(cron = "${gimee.trading.check-paper-trade.cron}")
    fun scheduleItemProcessing() {
        val paperTrades = paperTradeFinderService.findAllActive()

        logger.info("Check PaperTrade sell cron begin, there are ${paperTrades.size} paper trades to check.")

        runBlocking {
            paperTrades.chunked(100).forEach { batch ->
                batch.map { paperTrade ->
                    scope.async {
                        concurrencyLimiter.withPermit {
                            try {
                                checkPaperTradeStateService.check(paperTrade.id)
                            } catch (e: Exception) {
                                logger.error("failed to process PaperTrade with id=${paperTrade.id}", e)
                            }
                        }
                    }
                }.awaitAll()
            }
        }

        logger.info("Check PaperTrade sell cron finished.")
    }
}

```

### Fixed Rate Job

```kotlin
@Component
class EvmPaymentEventMonitoringTrigger(
    private val evmPaymentEventProcessingService: EvmPaymentEventProcessingService,
) {

    private val logger = logger()

    @Scheduled(fixedRateString = "${gimee.subscription.payment.monitoring.scheduled-fixed-delay}")
    @SchedulerLock(
        name = "CHECK_PAYMENT_EVENTS",
        lockAtLeastFor = "${gimee.subscription.payment.monitoring.lock-for}",
        lockAtMostFor = "${tgimee.subscription.payment.monitoring.lock-for}",
    )
    fun checkPaymentEvents() {
        logger.debug("CHECK_PAYMENT_EVENTS scheduled task started")
        evmPaymentEventProcessingService.processPaymentEvents()
        logger.debug("CHECK_PAYMENT_EVENTS scheduled task finished")
    }
}

```
